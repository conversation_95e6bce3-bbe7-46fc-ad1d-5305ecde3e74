#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务器知识抽取器
从MCP服务器的复杂查询和响应中提取可复用的模式和知识
"""

import json
import re
import hashlib
import time
import os
from typing import Dict, List, Any, Optional, Tuple, Set
from datetime import datetime
from collections import Counter

from mcp_agent_kb_optimizer import MC<PERSON>gentKBOptimizer, KnowledgeFragment

class MCPKnowledgeExtractor:
    """MCP服务器知识抽取器"""

    def __init__(self, optimizer: MCPAgentKBOptimizer):
        self.optimizer = optimizer
        self.query_history = []
        self.error_patterns = {}
        self.field_usage_stats = Counter()
        self.table_usage_stats = Counter()
        self.filter_patterns = {}

    def analyze_query(self, query_params: Dict[str, Any], response: Dict[str, Any],
                     execution_time: float) -> Dict[str, Any]:
        """分析查询和响应，提取知识"""
        # 记录查询历史
        self.query_history.append({
            "query": query_params,
            "response": response,
            "time": execution_time,
            "timestamp": datetime.now()
        })

        # 更新使用统计
        self._update_usage_stats(query_params)

        # 提取知识
        extracted_knowledge = {
            "patterns": self._extract_query_patterns(query_params, response),
            "errors": self._extract_error_patterns(response),
            "optimizations": self._generate_optimization_suggestions(query_params, response, execution_time)
        }

        # 存储提取的知识
        self._store_extracted_knowledge(extracted_knowledge)

        return extracted_knowledge

    def _update_usage_stats(self, query_params: Dict[str, Any]):
        """更新字段和表使用统计"""
        # 更新表使用统计
        table = query_params.get('table', '')
        if table:
            self.table_usage_stats[table] += 1

        # 更新字段使用统计
        fields = query_params.get('fields', [])
        if isinstance(fields, str):
            fields = [f.strip() for f in fields.split(',') if f.strip()]

        for field in fields:
            self.field_usage_stats[field] += 1

        # 更新过滤模式
        filter_condition = query_params.get('filter_condition', '')
        if filter_condition:
            # 提取过滤模式（简化版）
            simplified_filter = self._simplify_filter_pattern(filter_condition)
            if simplified_filter:
                if simplified_filter not in self.filter_patterns:
                    self.filter_patterns[simplified_filter] = {
                        "count": 0,
                        "examples": []
                    }

                self.filter_patterns[simplified_filter]["count"] += 1
                if len(self.filter_patterns[simplified_filter]["examples"]) < 5:
                    self.filter_patterns[simplified_filter]["examples"].append(filter_condition)

    def _simplify_filter_pattern(self, filter_condition: str) -> str:
        """简化过滤条件，提取模式"""
        # 替换具体值为占位符
        pattern = re.sub(r'=\d+', '=<NUM>', filter_condition)
        pattern = re.sub(r'="[^"]+"', '=<STR>', pattern)
        pattern = re.sub(r'=\'[^\']+\'', '=<STR>', pattern)
        pattern = re.sub(r'>\d+', '><NUM>', pattern)
        pattern = re.sub(r'<\d+', '<<NUM>', pattern)

        return pattern

    def _extract_query_patterns(self, query_params: Dict[str, Any],
                              response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取查询模式"""
        patterns = []

        # 检查是否是成功的查询
        if response.get('success', False):
            # 提取基本查询模式
            table = query_params.get('table', '')
            fields = query_params.get('fields', [])
            keys = query_params.get('keys', [])

            if isinstance(fields, str):
                fields = [f.strip() for f in fields.split(',') if f.strip()]

            if isinstance(keys, str):
                keys = [k.strip() for k in keys.split(',') if k.strip()]

            # 创建查询模式
            if table and (fields or keys):
                pattern = {
                    "table": table,
                    "fields": fields,
                    "keys": keys,
                    "time_pattern": self._extract_time_pattern(query_params),
                    "filter_type": self._categorize_filter(query_params.get('filter_condition', '')),
                    "success": True,
                    "data_size": len(response.get('data', '')) if isinstance(response.get('data'), str) else 0
                }
                patterns.append(pattern)

        return patterns

    def _extract_time_pattern(self, query_params: Dict[str, Any]) -> str:
        """提取时间模式"""
        begintime = query_params.get('begintime', '')
        endtime = query_params.get('endtime', '')

        if not begintime or not endtime:
            return "unknown"

        # 简单的时间模式识别
        if "00:00:00" in begintime and "23:59:59" in endtime:
            return "full_day"
        elif begintime.endswith("00:00:00"):
            return "from_start_of_day"
        else:
            return "custom_time_range"

    def _categorize_filter(self, filter_condition: str) -> str:
        """对过滤条件进行分类"""
        if not filter_condition:
            return "none"

        if "&&" in filter_condition and "||" in filter_condition:
            return "complex"
        elif "&&" in filter_condition:
            return "and_condition"
        elif "||" in filter_condition:
            return "or_condition"
        else:
            return "simple"

    def _extract_error_patterns(self, response: Dict[str, Any]) -> List[Dict[str, Any]]:
        """提取错误模式"""
        errors = []

        if not response.get('success', True):
            error_code = response.get('error_code', -1)
            error_message = response.get('message', '')

            # 创建错误模式
            error_pattern = {
                "error_code": error_code,
                "error_type": self._categorize_error(error_code, error_message),
                "message": error_message,
                "timestamp": datetime.now()
            }

            errors.append(error_pattern)

            # 更新错误统计
            error_key = f"{error_code}_{self._categorize_error(error_code, error_message)}"
            if error_key not in self.error_patterns:
                self.error_patterns[error_key] = {
                    "count": 0,
                    "examples": []
                }

            self.error_patterns[error_key]["count"] += 1
            if len(self.error_patterns[error_key]["examples"]) < 3:
                self.error_patterns[error_key]["examples"].append(error_message)

        return errors

    def _categorize_error(self, error_code: int, error_message: str) -> str:
        """对错误进行分类"""
        if error_code == 5:
            return "authentication"
        elif error_code in [14, 21]:
            return "table_not_found"
        elif error_code in [13, 22]:
            return "field_error"
        elif error_code == 16:
            return "time_format"
        elif error_code == 26:
            return "parameter_error"
        elif "timeout" in error_message.lower():
            return "timeout"
        elif "connection" in error_message.lower():
            return "connection"
        else:
            return "unknown"

    def _generate_optimization_suggestions(self, query_params: Dict[str, Any],
                                         response: Dict[str, Any],
                                         execution_time: float) -> List[Dict[str, Any]]:
        """生成优化建议"""
        suggestions = []

        # 基于执行时间的建议
        if execution_time > 10.0:
            suggestions.append({
                "type": "performance",
                "suggestion": "查询执行时间较长，建议减少返回记录数或优化过滤条件",
                "priority": "high",
                "details": f"执行时间: {execution_time:.2f}秒"
            })

        # 基于数据量的建议
        if response.get('success', False):
            data_size = len(response.get('data', '')) if isinstance(response.get('data'), str) else 0
            if data_size > 100000:  # 大于100KB
                suggestions.append({
                    "type": "data_size",
                    "suggestion": "返回数据量较大，建议使用分页或减少字段",
                    "priority": "medium",
                    "details": f"数据大小: {data_size} 字节"
                })

        # 基于字段使用的建议
        fields = query_params.get('fields', [])
        if isinstance(fields, str):
            fields = [f.strip() for f in fields.split(',') if f.strip()]

        if len(fields) > 10:
            suggestions.append({
                "type": "field_count",
                "suggestion": "查询字段过多，建议只选择必要的字段",
                "priority": "low",
                "details": f"字段数量: {len(fields)}"
            })

        # 基于过滤条件的建议
        filter_condition = query_params.get('filter_condition', '')
        if filter_condition and len(filter_condition) > 200:
            suggestions.append({
                "type": "filter_complexity",
                "suggestion": "过滤条件过于复杂，建议简化或分步查询",
                "priority": "medium",
                "details": f"过滤条件长度: {len(filter_condition)}"
            })

        return suggestions

    def _store_extracted_knowledge(self, extracted_knowledge: Dict[str, Any]):
        """存储提取的知识到Agent-KB"""
        # 存储查询模式知识
        for pattern in extracted_knowledge.get("patterns", []):
            if pattern.get("success", False):
                content = f"表 {pattern['table']} 的查询模式：字段 {pattern['fields']}，键 {pattern['keys']}，时间模式 {pattern['time_pattern']}"
                triggers = [pattern['table']] + pattern['fields'] + pattern['keys']

                self.optimizer.add_knowledge_fragment(
                    knowledge_type="query_pattern",
                    content=content,
                    triggers=triggers,
                    source="auto_extraction"
                )

        # 存储错误处理知识
        for error in extracted_knowledge.get("errors", []):
            content = f"错误码 {error['error_code']} ({error['error_type']}): {error['message']}"
            triggers = [str(error['error_code']), error['error_type']]

            self.optimizer.add_knowledge_fragment(
                knowledge_type="error_handling",
                content=content,
                triggers=triggers,
                source="auto_extraction"
            )

        # 存储优化建议知识
        for suggestion in extracted_knowledge.get("optimizations", []):
            content = f"{suggestion['type']} 优化: {suggestion['suggestion']}"
            triggers = [suggestion['type'], "optimization", "performance"]

            self.optimizer.add_knowledge_fragment(
                knowledge_type="query_optimization",
                content=content,
                triggers=triggers,
                source="auto_extraction"
            )

    def get_usage_statistics(self) -> Dict[str, Any]:
        """获取使用统计信息"""
        return {
            "most_used_tables": dict(self.table_usage_stats.most_common(10)),
            "most_used_fields": dict(self.field_usage_stats.most_common(20)),
            "common_filter_patterns": {
                pattern: info for pattern, info in self.filter_patterns.items()
                if info["count"] >= 2
            },
            "error_patterns": self.error_patterns,
            "total_queries": len(self.query_history)
        }

    def generate_microagent_knowledge(self) -> List[Dict[str, Any]]:
        """生成microagent格式的知识"""
        microagents = []

        # 为最常用的表生成microagent
        for table, count in self.table_usage_stats.most_common(5):
            # 获取该表的常用字段
            table_fields = []
            for query in self.query_history:
                if query["query"].get("table") == table:
                    fields = query["query"].get("fields", [])
                    if isinstance(fields, str):
                        fields = [f.strip() for f in fields.split(',') if f.strip()]
                    table_fields.extend(fields)

            common_fields = [field for field, _ in Counter(table_fields).most_common(10)]

            microagent = {
                "name": f"{table}_expert",
                "type": "knowledge",
                "triggers": [table, f"{table}_query", f"{table}_statistics"],
                "content": f"""# {table} 表查询专家

## 常用字段
{', '.join(common_fields)}

## 使用频率
该表被查询了 {count} 次

## 优化建议
- 优先使用常用字段组合
- 注意时间范围设置
- 合理使用过滤条件
""",
                "confidence": min(0.9, count / max(self.table_usage_stats.values()))
            }
            microagents.append(microagent)

        # 为常见错误生成microagent
        if self.error_patterns:
            error_content = "# MCP查询错误处理指南\n\n"
            for error_key, error_info in self.error_patterns.items():
                error_content += f"## {error_key}\n"
                error_content += f"出现次数: {error_info['count']}\n"
                if error_info['examples']:
                    error_content += f"示例: {error_info['examples'][0]}\n"
                error_content += "\n"

            error_microagent = {
                "name": "mcp_error_handler",
                "type": "knowledge",
                "triggers": ["error", "错误", "失败", "问题"],
                "content": error_content,
                "confidence": 0.8
            }
            microagents.append(error_microagent)

        return microagents

    def export_knowledge_base(self, filepath: str):
        """导出知识库到文件"""
        knowledge_export = {
            "usage_statistics": self.get_usage_statistics(),
            "microagents": self.generate_microagent_knowledge(),
            "query_history_summary": {
                "total_queries": len(self.query_history),
                "success_rate": sum(1 for q in self.query_history if q["response"].get("success", False)) / max(len(self.query_history), 1),
                "avg_execution_time": sum(q["time"] for q in self.query_history) / max(len(self.query_history), 1),
                "date_range": {
                    "start": min(q["timestamp"] for q in self.query_history) if self.query_history else None,
                    "end": max(q["timestamp"] for q in self.query_history) if self.query_history else None
                }
            },
            "export_timestamp": datetime.now()
        }

        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(knowledge_export, f, ensure_ascii=False, indent=2, default=str)

    def clear_history(self, keep_recent: int = 100):
        """清理历史记录，保留最近的记录"""
        if len(self.query_history) > keep_recent:
            self.query_history = self.query_history[-keep_recent:]