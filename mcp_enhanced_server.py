#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强版MCP服务器 - 集成Agent-KB优化
结合Gemini复杂任务处理和Qwen3 API复用的完整优化流程
"""

import json
import time
import traceback
from typing import Dict, List, Any, Optional
from datetime import datetime

# 导入原始MCP服务器的所有功能
from stats_mcp_server_official import *

# 导入我们的优化组件
from mcp_agent_kb_optimizer import MCPAgentKBOptimizer
from mcp_knowledge_extractor import MCPKnowledgeExtractor
from mcp_microagent_memory import MCPMicroAgentMemory

# 创建优化组件实例
optimizer = MCPAgentKBOptimizer()
extractor = MCPKnowledgeExtractor(optimizer)
memory = MCPMicroAgentMemory()

# 模拟的Gemini和Qwen3 API接口
class GeminiAPI:
    """模拟Gemini API接口"""

    @staticmethod
    def analyze_complex_query(query_params: Dict[str, Any], context: Dict[str, Any]) -> Dict[str, Any]:
        """使用Gemini分析复杂查询"""
        # 这里应该调用真实的Gemini API
        # 现在返回模拟的分析结果

        complexity = optimizer.analyze_query_complexity(query_params)

        return {
            "intent": f"分析{query_params.get('table', '')}表的统计数据",
            "reasoning": f"基于查询复杂度{complexity:.2f}，建议的优化策略包括：时间范围优化、字段选择优化、过滤条件简化",
            "optimized_params": {
                **query_params,
                "topcount": min(query_params.get("topcount", 1000), 5000),  # 限制返回记录数
                "suggested_fields": ["server_ip_addr", "total_byte", "total_packet"]  # 建议字段
            },
            "steps": [
                {"step": 1, "action": "验证查询参数", "details": "检查表名、字段名、时间格式"},
                {"step": 2, "action": "优化查询条件", "details": "简化过滤条件，优化字段选择"},
                {"step": 3, "action": "执行查询", "details": "使用优化后的参数执行查询"},
                {"step": 4, "action": "分析结果", "details": "验证查询结果并提供优化建议"}
            ],
            "validation": {
                "parameter_check": "passed",
                "complexity_assessment": f"complexity_score: {complexity:.2f}",
                "optimization_potential": "medium"
            }
        }

class Qwen3API:
    """模拟Qwen3 API接口"""

    @staticmethod
    def execute_optimized_query(query_params: Dict[str, Any],
                              optimization_hints: Dict[str, Any]) -> Dict[str, Any]:
        """使用Qwen3执行优化后的查询"""
        # 这里应该调用真实的Qwen3 API
        # 现在使用原始的查询逻辑，但加入优化提示

        start_time = time.time()

        # 应用优化提示
        if "suggested_fields" in optimization_hints:
            if not query_params.get("fields"):
                query_params["fields"] = optimization_hints["suggested_fields"]

        # 执行原始查询逻辑
        result = query_statistics_table(
            table=query_params.get("table", ""),
            begintime=query_params.get("begintime", ""),
            endtime=query_params.get("endtime", ""),
            fields=query_params.get("fields", ""),
            keys=query_params.get("keys", ""),
            timeunit=query_params.get("timeunit", 0),
            filter_condition=query_params.get("filter_condition", ""),
            topcount=query_params.get("topcount", 1000),
            sortfield=query_params.get("sortfield", "total_byte"),
            sorttype=query_params.get("sorttype", 2),
            netlink=query_params.get("netlink", 2)
        )

        execution_time = time.time() - start_time

        # 解析结果
        try:
            result_dict = json.loads(result)
            result_dict["execution_time"] = execution_time
            result_dict["optimization_applied"] = True
            result_dict["optimization_hints"] = optimization_hints
            return result_dict
        except:
            return {
                "success": False,
                "message": "查询结果解析失败",
                "execution_time": execution_time,
                "optimization_applied": True
            }

@mcp.tool()
def enhanced_query_statistics_table(
    table: str,
    begintime: str,
    endtime: str,
    fields: str,
    keys: str,
    timeunit: int = 0,
    filter_condition: str = "",
    topcount: int = 1000,
    sortfield: str = "total_byte",
    sorttype: int = 2,
    netlink: int = 2,
    use_optimization: bool = True
) -> str:
    """
    增强版统计表查询 - 集成Agent-KB优化

    Args:
        table: 统计表名
        begintime: 开始时间
        endtime: 结束时间
        fields: 查询字段
        keys: 键字段
        timeunit: 时间单位
        filter_condition: 过滤条件
        topcount: 返回记录数
        sortfield: 排序字段
        sorttype: 排序类型
        netlink: 网络链路ID
        use_optimization: 是否使用Agent-KB优化

    Returns:
        查询结果的JSON字符串，包含优化信息
    """
    global api_client, optimizer, extractor, memory

    if api_client is None:
        result = {
            "success": False,
            "message": "请先使用 setup_api_connection 设置API连接"
        }
        return json.dumps(result, ensure_ascii=False, indent=2)

    # 构建查询参数
    query_params = {
        "table": table,
        "begintime": begintime,
        "endtime": endtime,
        "fields": fields,
        "keys": keys,
        "timeunit": timeunit,
        "filter_condition": filter_condition,
        "topcount": topcount,
        "sortfield": sortfield,
        "sorttype": sorttype,
        "netlink": netlink
    }

    start_time = time.time()

    try:
        if use_optimization:
            # 1. 检查是否需要使用Gemini
            should_use_gemini, reason = optimizer.should_use_gemini(query_params)

            # 2. 查找相关知识
            relevant_knowledge = memory.find_relevant_knowledge(json.dumps(query_params))

            if should_use_gemini:
                # 使用Gemini处理复杂查询
                print(f"使用Gemini处理复杂查询: {reason}")

                # 构建上下文
                context = {
                    "relevant_knowledge": relevant_knowledge,
                    "query_history": extractor.get_usage_statistics()
                }

                # Gemini分析
                gemini_response = GeminiAPI.analyze_complex_query(query_params, context)

                # 使用优化后的参数执行查询
                optimized_params = gemini_response.get("optimized_params", query_params)
                result = Qwen3API.execute_optimized_query(optimized_params, gemini_response)

                # 记录性能指标
                execution_time = time.time() - start_time
                performance_metrics = {
                    "response_time": execution_time,
                    "optimization_used": "gemini",
                    "complexity_score": optimizer.analyze_query_complexity(query_params)
                }

                # 存储Gemini解决方案
                solution_id = optimizer.store_gemini_solution(
                    query_params, gemini_response, performance_metrics
                )

                result["optimization_info"] = {
                    "method": "gemini",
                    "reason": reason,
                    "solution_id": solution_id,
                    "gemini_analysis": gemini_response,
                    "relevant_knowledge": relevant_knowledge
                }

            else:
                # 检查是否有可复用的模式
                qwen3_optimization = optimizer.get_qwen3_optimization(query_params)

                if qwen3_optimization:
                    # 使用Qwen3复用已有模式
                    print(f"使用Qwen3复用模式: {reason}")

                    result = Qwen3API.execute_optimized_query(query_params, qwen3_optimization)

                    result["optimization_info"] = {
                        "method": "qwen3_reuse",
                        "reason": reason,
                        "pattern_info": qwen3_optimization,
                        "relevant_knowledge": relevant_knowledge
                    }
                else:
                    # 直接执行查询
                    result_str = query_statistics_table(
                        table, begintime, endtime, fields, keys, timeunit,
                        filter_condition, topcount, sortfield, sorttype, netlink
                    )
                    result = json.loads(result_str)

                    result["optimization_info"] = {
                        "method": "direct",
                        "reason": reason,
                        "relevant_knowledge": relevant_knowledge
                    }
        else:
            # 不使用优化，直接执行原始查询
            result_str = query_statistics_table(
                table, begintime, endtime, fields, keys, timeunit,
                filter_condition, topcount, sortfield, sorttype, netlink
            )
            result = json.loads(result_str)
            result["optimization_info"] = {"method": "none"}

        # 分析查询结果并提取知识
        execution_time = time.time() - start_time
        extracted_knowledge = extractor.analyze_query(query_params, result, execution_time)

        result["extracted_knowledge"] = extracted_knowledge
        result["total_execution_time"] = execution_time

        return json.dumps(result, ensure_ascii=False, indent=2)

    except Exception as e:
        error_result = {
            "success": False,
            "message": f"增强查询失败: {str(e)}",
            "traceback": traceback.format_exc(),
            "execution_time": time.time() - start_time
        }
        return json.dumps(error_result, ensure_ascii=False, indent=2)

@mcp.tool()
def get_optimization_statistics() -> str:
    """
    获取优化统计信息

    Returns:
        优化统计信息的JSON字符串
    """
    global optimizer, extractor, memory

    try:
        # 获取优化器性能报告
        optimizer_stats = optimizer.get_performance_report()

        # 获取知识提取器使用统计
        extractor_stats = extractor.get_usage_statistics()

        # 获取microagent内存信息
        memory_stats = memory.get_all_agents()

        # 合并统计信息
        stats = {
            "optimizer_stats": optimizer_stats,
            "extractor_stats": extractor_stats,
            "memory_stats": {
                "knowledge_agents_count": len(memory_stats["knowledge_agents"]),
                "task_agents_count": len(memory_stats["task_agents"]),
                "most_used_agents": sorted(
                    memory_stats["knowledge_agents"],
                    key=lambda x: x["usage_count"],
                    reverse=True
                )[:5]
            },
            "timestamp": datetime.now()
        }

        return json.dumps(stats, ensure_ascii=False, indent=2, default=str)

    except Exception as e:
        error_result = {
            "success": False,
            "message": f"获取优化统计信息失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(error_result, ensure_ascii=False, indent=2)

@mcp.tool()
def add_knowledge_to_memory(
    name: str,
    content: str,
    triggers: str,
    knowledge_type: str = "knowledge"
) -> str:
    """
    添加知识到Agent-KB内存

    Args:
        name: 知识名称
        content: 知识内容（Markdown格式）
        triggers: 触发关键词，逗号分隔
        knowledge_type: 知识类型 (knowledge 或 task)

    Returns:
        添加结果的JSON字符串
    """
    global memory

    try:
        # 解析触发器
        trigger_list = [t.strip() for t in triggers.split(',') if t.strip()]

        if knowledge_type == "knowledge":
            agent = memory.add_knowledge_agent(name, content, trigger_list)
        elif knowledge_type == "task":
            agent = memory.add_task_agent(name, content, trigger_list)
        else:
            return json.dumps({
                "success": False,
                "message": f"不支持的知识类型: {knowledge_type}，请使用 knowledge 或 task"
            }, ensure_ascii=False, indent=2)

        return json.dumps({
            "success": True,
            "message": f"成功添加{knowledge_type}类型知识: {name}",
            "agent_info": {
                "name": agent.name,
                "type": agent.agent_type,
                "triggers": agent.triggers,
                "created_at": agent.created_at
            }
        }, ensure_ascii=False, indent=2, default=str)

    except Exception as e:
        error_result = {
            "success": False,
            "message": f"添加知识失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(error_result, ensure_ascii=False, indent=2)

@mcp.tool()
def export_knowledge_base(export_path: str = "./mcp_knowledge_export.json") -> str:
    """
    导出知识库

    Args:
        export_path: 导出文件路径

    Returns:
        导出结果的JSON字符串
    """
    global extractor

    try:
        # 导出知识库
        extractor.export_knowledge_base(export_path)

        return json.dumps({
            "success": True,
            "message": f"成功导出知识库到: {export_path}",
            "export_time": datetime.now()
        }, ensure_ascii=False, indent=2, default=str)

    except Exception as e:
        error_result = {
            "success": False,
            "message": f"导出知识库失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(error_result, ensure_ascii=False, indent=2)

@mcp.tool()
def find_knowledge(query: str) -> str:
    """
    查找相关知识

    Args:
        query: 查询关键词

    Returns:
        相关知识的JSON字符串
    """
    global memory

    try:
        # 查找相关知识
        relevant_knowledge = memory.find_relevant_knowledge(query)

        return json.dumps({
            "success": True,
            "query": query,
            "knowledge_count": len(relevant_knowledge),
            "knowledge": relevant_knowledge
        }, ensure_ascii=False, indent=2, default=str)

    except Exception as e:
        error_result = {
            "success": False,
            "message": f"查找知识失败: {str(e)}",
            "traceback": traceback.format_exc()
        }
        return json.dumps(error_result, ensure_ascii=False, indent=2)

# 如果直接运行此脚本，启动MCP服务器
if __name__ == '__main__':
    print("启动增强版MCP服务器...")
    print("已加载Agent-KB优化组件:")
    print(f"- 优化器: {optimizer.__class__.__name__}")
    print(f"- 知识提取器: {extractor.__class__.__name__}")
    print(f"- 内存系统: {memory.__class__.__name__}")
    print("使用 enhanced_query_statistics_table 工具进行优化查询")
    mcp.run()