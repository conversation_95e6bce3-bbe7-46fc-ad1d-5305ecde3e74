#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务器Agent-KB优化器
结合Gemini复杂任务处理和Qwen3 API复用的专有优化系统
"""

import json
import hashlib
import time
import os
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime
import pickle

@dataclass
class QueryPattern:
    """查询模式类 - 存储可复用的查询模式"""
    pattern_id: str
    table: str
    fields: List[str]
    keys: List[str]
    time_pattern: str  # 时间模式，如"last_hour", "last_day"
    filter_pattern: str
    complexity_score: float
    success_rate: float
    avg_response_time: float
    usage_count: int
    created_by: str  # "gemini" or "qwen3"
    created_at: datetime
    last_used: datetime

@dataclass
class QuerySolution:
    """查询解决方案 - 存储Gemini生成的复杂查询解决方案"""
    solution_id: str
    original_query: str
    query_intent: str
    solution_steps: List[Dict[str, Any]]
    optimized_params: Dict[str, Any]
    performance_metrics: Dict[str, float]
    gemini_reasoning: str
    validation_results: Dict[str, Any]
    created_at: datetime

@dataclass
class KnowledgeFragment:
    """知识片段 - 存储领域特定知识"""
    fragment_id: str
    knowledge_type: str  # "table_schema", "query_optimization", "error_handling"
    content: str
    triggers: List[str]  # 触发关键词
    confidence: float
    source: str  # "gemini_analysis", "user_feedback", "system_learning"
    created_at: datetime

class MCPAgentKBOptimizer:
    """MCP服务器Agent-KB优化器主类"""

    def __init__(self, knowledge_base_path: str = "./mcp_knowledge_base"):
        self.kb_path = knowledge_base_path
        self.patterns_file = os.path.join(self.kb_path, "query_patterns.json")
        self.solutions_file = os.path.join(self.kb_path, "query_solutions.json")
        self.knowledge_file = os.path.join(self.kb_path, "knowledge_fragments.json")

        # 确保知识库目录存在
        os.makedirs(self.kb_path, exist_ok=True)

        # 加载现有知识
        self.query_patterns: Dict[str, QueryPattern] = self._load_patterns()
        self.query_solutions: Dict[str, QuerySolution] = self._load_solutions()
        self.knowledge_fragments: Dict[str, KnowledgeFragment] = self._load_knowledge()

        # 性能统计
        self.performance_stats = {
            "gemini_queries": 0,
            "qwen3_reused": 0,
            "pattern_matches": 0,
            "total_time_saved": 0.0
        }

    def _load_patterns(self) -> Dict[str, QueryPattern]:
        """加载查询模式"""
        if os.path.exists(self.patterns_file):
            with open(self.patterns_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {k: QueryPattern(**v) for k, v in data.items()}
        return {}

    def _load_solutions(self) -> Dict[str, QuerySolution]:
        """加载查询解决方案"""
        if os.path.exists(self.solutions_file):
            with open(self.solutions_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {k: QuerySolution(**v) for k, v in data.items()}
        return {}

    def _load_knowledge(self) -> Dict[str, KnowledgeFragment]:
        """加载知识片段"""
        if os.path.exists(self.knowledge_file):
            with open(self.knowledge_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                return {k: KnowledgeFragment(**v) for k, v in data.items()}
        return {}

    def _save_patterns(self):
        """保存查询模式"""
        data = {k: asdict(v) for k, v in self.query_patterns.items()}
        with open(self.patterns_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def _save_solutions(self):
        """保存查询解决方案"""
        data = {k: asdict(v) for k, v in self.query_solutions.items()}
        with open(self.solutions_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def _save_knowledge(self):
        """保存知识片段"""
        data = {k: asdict(v) for k, v in self.knowledge_fragments.items()}
        with open(self.knowledge_file, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2, default=str)

    def generate_pattern_id(self, table: str, fields: List[str], keys: List[str],
                          time_pattern: str, filter_pattern: str) -> str:
        """生成查询模式ID"""
        pattern_str = f"{table}|{','.join(sorted(fields))}|{','.join(sorted(keys))}|{time_pattern}|{filter_pattern}"
        return hashlib.md5(pattern_str.encode()).hexdigest()[:16]

    def analyze_query_complexity(self, query_params: Dict[str, Any]) -> float:
        """分析查询复杂度"""
        complexity = 0.0

        # 基于字段数量
        fields_count = len(query_params.get('fields', []))
        complexity += fields_count * 0.1

        # 基于过滤条件复杂度
        filter_condition = query_params.get('filter_condition', '')
        if filter_condition:
            complexity += len(filter_condition.split('&&')) * 0.2
            complexity += len(filter_condition.split('||')) * 0.2

        # 基于时间范围
        timeunit = query_params.get('timeunit', 0)
        if timeunit > 0:
            complexity += 0.3

        # 基于返回记录数
        topcount = query_params.get('topcount', 1000)
        if topcount > 5000:
            complexity += 0.5

        return min(complexity, 10.0)  # 最大复杂度为10

    def should_use_gemini(self, query_params: Dict[str, Any]) -> Tuple[bool, str]:
        """判断是否应该使用Gemini处理查询"""
        complexity = self.analyze_query_complexity(query_params)

        # 检查是否有匹配的模式
        pattern_id = self.generate_pattern_id(
            query_params.get('table', ''),
            query_params.get('fields', []),
            query_params.get('keys', []),
            self._extract_time_pattern(query_params),
            query_params.get('filter_condition', '')
        )

        if pattern_id in self.query_patterns:
            pattern = self.query_patterns[pattern_id]
            if pattern.success_rate > 0.8:
                return False, f"使用已有模式 {pattern_id}，成功率: {pattern.success_rate:.2f}"

        # 复杂度阈值判断
        if complexity > 3.0:
            return True, f"查询复杂度 {complexity:.2f} 超过阈值，使用Gemini处理"

        return False, f"查询复杂度 {complexity:.2f} 较低，可直接处理"

    def _extract_time_pattern(self, query_params: Dict[str, Any]) -> str:
        """提取时间模式"""
        begintime = query_params.get('begintime', '')
        endtime = query_params.get('endtime', '')

        if not begintime or not endtime:
            return "unknown"

        # 简单的时间模式识别
        if "00:00:00" in begintime and "23:59:59" in endtime:
            return "full_day"
        elif begintime.endswith("00:00:00"):
            return "from_start_of_day"
        else:
            return "custom_time_range"

    def store_gemini_solution(self, query_params: Dict[str, Any],
                            gemini_response: Dict[str, Any],
                            performance_metrics: Dict[str, float]) -> str:
        """存储Gemini生成的解决方案"""
        solution_id = hashlib.md5(
            f"{json.dumps(query_params, sort_keys=True)}{time.time()}"
            .encode()
        ).hexdigest()[:16]

        solution = QuerySolution(
            solution_id=solution_id,
            original_query=json.dumps(query_params),
            query_intent=gemini_response.get('intent', ''),
            solution_steps=gemini_response.get('steps', []),
            optimized_params=gemini_response.get('optimized_params', {}),
            performance_metrics=performance_metrics,
            gemini_reasoning=gemini_response.get('reasoning', ''),
            validation_results=gemini_response.get('validation', {}),
            created_at=datetime.now()
        )

        self.query_solutions[solution_id] = solution
        self._save_solutions()

        # 同时创建查询模式
        self._create_pattern_from_solution(query_params, solution, performance_metrics)

        self.performance_stats["gemini_queries"] += 1
        return solution_id

    def _create_pattern_from_solution(self, query_params: Dict[str, Any],
                                    solution: QuerySolution,
                                    performance_metrics: Dict[str, float]):
        """从解决方案创建查询模式"""
        pattern_id = self.generate_pattern_id(
            query_params.get('table', ''),
            query_params.get('fields', []),
            query_params.get('keys', []),
            self._extract_time_pattern(query_params),
            query_params.get('filter_condition', '')
        )

        pattern = QueryPattern(
            pattern_id=pattern_id,
            table=query_params.get('table', ''),
            fields=query_params.get('fields', []),
            keys=query_params.get('keys', []),
            time_pattern=self._extract_time_pattern(query_params),
            filter_pattern=query_params.get('filter_condition', ''),
            complexity_score=self.analyze_query_complexity(query_params),
            success_rate=1.0,  # 初始成功率
            avg_response_time=performance_metrics.get('response_time', 0.0),
            usage_count=1,
            created_by="gemini",
            created_at=datetime.now(),
            last_used=datetime.now()
        )

        self.query_patterns[pattern_id] = pattern
        self._save_patterns()

    def get_qwen3_optimization(self, query_params: Dict[str, Any]) -> Optional[Dict[str, Any]]:
        """为Qwen3获取优化建议"""
        pattern_id = self.generate_pattern_id(
            query_params.get('table', ''),
            query_params.get('fields', []),
            query_params.get('keys', []),
            self._extract_time_pattern(query_params),
            query_params.get('filter_condition', '')
        )

        if pattern_id in self.query_patterns:
            pattern = self.query_patterns[pattern_id]

            # 更新使用统计
            pattern.usage_count += 1
            pattern.last_used = datetime.now()
            self._save_patterns()

            self.performance_stats["qwen3_reused"] += 1
            self.performance_stats["pattern_matches"] += 1

            return {
                "pattern_found": True,
                "pattern_id": pattern_id,
                "success_rate": pattern.success_rate,
                "avg_response_time": pattern.avg_response_time,
                "optimization_suggestions": self._get_optimization_suggestions(pattern),
                "related_knowledge": self._get_related_knowledge(query_params)
            }

        return None

    def _get_optimization_suggestions(self, pattern: QueryPattern) -> List[str]:
        """获取优化建议"""
        suggestions = []

        if pattern.avg_response_time > 5.0:
            suggestions.append("考虑减少返回记录数或优化过滤条件")

        if pattern.complexity_score > 5.0:
            suggestions.append("查询较复杂，建议分步执行")

        if pattern.success_rate < 0.9:
            suggestions.append("该模式成功率较低，建议检查参数")

        return suggestions

    def _get_related_knowledge(self, query_params: Dict[str, Any]) -> List[Dict[str, Any]]:
        """获取相关知识片段"""
        related = []
        query_text = json.dumps(query_params).lower()

        for fragment in self.knowledge_fragments.values():
            for trigger in fragment.triggers:
                if trigger.lower() in query_text:
                    related.append({
                        "type": fragment.knowledge_type,
                        "content": fragment.content,
                        "confidence": fragment.confidence
                    })
                    break

        return related[:5]  # 返回最多5个相关知识片段

    def add_knowledge_fragment(self, knowledge_type: str, content: str,
                             triggers: List[str], source: str = "manual") -> str:
        """添加知识片段"""
        fragment_id = hashlib.md5(f"{knowledge_type}{content}".encode()).hexdigest()[:16]

        fragment = KnowledgeFragment(
            fragment_id=fragment_id,
            knowledge_type=knowledge_type,
            content=content,
            triggers=triggers,
            confidence=0.8,  # 默认置信度
            source=source,
            created_at=datetime.now()
        )

        self.knowledge_fragments[fragment_id] = fragment
        self._save_knowledge()
        return fragment_id

    def get_performance_report(self) -> Dict[str, Any]:
        """获取性能报告"""
        total_queries = self.performance_stats["gemini_queries"] + self.performance_stats["qwen3_reused"]

        return {
            "total_queries": total_queries,
            "gemini_usage": self.performance_stats["gemini_queries"],
            "qwen3_reuse": self.performance_stats["qwen3_reused"],
            "reuse_rate": self.performance_stats["qwen3_reused"] / max(total_queries, 1),
            "pattern_matches": self.performance_stats["pattern_matches"],
            "total_patterns": len(self.query_patterns),
            "total_solutions": len(self.query_solutions),
            "total_knowledge": len(self.knowledge_fragments),
            "estimated_time_saved": self.performance_stats["total_time_saved"]
        }