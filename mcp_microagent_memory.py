#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MCP服务器Agent-KB内存集成
基于Agent-KB-SWE-bench的microagent模式，实现MCP服务器的知识存储和检索系统
"""

import os
import json
import hashlib
from typing import Dict, List, Any, Optional
from datetime import datetime
from dataclasses import dataclass

@dataclass
class MCPMicroAgent:
    """MCP专用的MicroAgent"""
    name: str
    agent_type: str  # "knowledge" or "task"
    version: str
    triggers: List[str]
    content: str
    confidence: float
    created_at: datetime
    last_used: datetime
    usage_count: int

    def match_trigger(self, query: str) -> Optional[str]:
        """检查查询是否匹配触发器"""
        query_lower = query.lower()
        for trigger in self.triggers:
            if trigger.lower() in query_lower:
                return trigger
        return None

class MCPMicroAgentMemory:
    """MCP服务器的MicroAgent内存系统"""

    def __init__(self, microagents_dir: str = "./mcp_microagents"):
        self.microagents_dir = microagents_dir
        self.knowledge_agents: Dict[str, MCPMicroAgent] = {}
        self.task_agents: Dict[str, MCPMicroAgent] = {}

        # 确保目录存在
        os.makedirs(microagents_dir, exist_ok=True)
        os.makedirs(os.path.join(microagents_dir, "knowledge"), exist_ok=True)
        os.makedirs(os.path.join(microagents_dir, "tasks"), exist_ok=True)

        # 加载现有的microagents
        self._load_microagents()

        # 创建默认的MCP知识microagents
        self._create_default_microagents()

    def _load_microagents(self):
        """加载现有的microagents"""
        # 加载知识型microagents
        knowledge_dir = os.path.join(self.microagents_dir, "knowledge")
        if os.path.exists(knowledge_dir):
            for filename in os.listdir(knowledge_dir):
                if filename.endswith('.md'):
                    filepath = os.path.join(knowledge_dir, filename)
                    agent = self._load_microagent_from_file(filepath, "knowledge")
                    if agent:
                        self.knowledge_agents[agent.name] = agent

        # 加载任务型microagents
        tasks_dir = os.path.join(self.microagents_dir, "tasks")
        if os.path.exists(tasks_dir):
            for filename in os.listdir(tasks_dir):
                if filename.endswith('.md'):
                    filepath = os.path.join(tasks_dir, filename)
                    agent = self._load_microagent_from_file(filepath, "task")
                    if agent:
                        self.task_agents[agent.name] = agent

    def _load_microagent_from_file(self, filepath: str, agent_type: str) -> Optional[MCPMicroAgent]:
        """从文件加载microagent"""
        try:
            with open(filepath, 'r', encoding='utf-8') as f:
                content = f.read()

            # 解析YAML前置内容
            if content.startswith('---'):
                parts = content.split('---', 2)
                if len(parts) >= 3:
                    yaml_content = parts[1].strip()
                    md_content = parts[2].strip()

                    # 简单解析YAML（仅支持基本格式）
                    metadata = {}
                    for line in yaml_content.split('\n'):
                        if ':' in line:
                            key, value = line.split(':', 1)
                            key = key.strip()
                            value = value.strip()

                            if key == 'triggers':
                                # 解析触发器列表
                                triggers = []
                                if value.startswith('[') and value.endswith(']'):
                                    # 简单的列表解析
                                    trigger_str = value[1:-1]
                                    for trigger in trigger_str.split(','):
                                        triggers.append(trigger.strip().strip('"\''))
                                metadata[key] = triggers
                            else:
                                metadata[key] = value.strip('"\'')

                    return MCPMicroAgent(
                        name=metadata.get('name', os.path.splitext(os.path.basename(filepath))[0]),
                        agent_type=agent_type,
                        version=metadata.get('version', '1.0.0'),
                        triggers=metadata.get('triggers', []),
                        content=md_content,
                        confidence=0.8,
                        created_at=datetime.now(),
                        last_used=datetime.now(),
                        usage_count=0
                    )
        except Exception as e:
            print(f"Error loading microagent from {filepath}: {e}")

        return None

    def _create_default_microagents(self):
        """创建默认的MCP知识microagents"""
        # 如果没有现有的microagents，创建一些默认的
        if not self.knowledge_agents:
            self._create_mcp_basics_agent()
            self._create_query_optimization_agent()
            self._create_error_handling_agent()

    def _create_mcp_basics_agent(self):
        """创建MCP基础知识agent"""
        content = """# MCP统计查询基础知识

## 常用统计表
- tcp_flow: TCP流量统计
- ip_flow: IP流量统计
- summary: 汇总统计
- application: 应用统计

## 基本查询参数
- table: 统计表名
- begintime/endtime: 时间范围
- fields: 查询字段
- keys: 键字段
- filter_condition: 过滤条件
- topcount: 返回记录数

## 时间格式
支持格式: YYYY-MM-DD HH:MM:SS 或 YYYY-MM-DD

## 常用字段组合
- 流量分析: server_ip_addr, total_byte, total_packet
- 性能分析: response_time, visit_count
- 安全分析: client_ip_addr, server_port, protocol
"""

        agent = MCPMicroAgent(
            name="mcp_basics",
            agent_type="knowledge",
            version="1.0.0",
            triggers=["mcp", "统计", "查询", "基础", "help"],
            content=content,
            confidence=0.9,
            created_at=datetime.now(),
            last_used=datetime.now(),
            usage_count=0
        )

        self.knowledge_agents[agent.name] = agent
        self._save_microagent(agent, "knowledge")

    def _create_query_optimization_agent(self):
        """创建查询优化知识agent"""
        content = """# MCP查询优化指南

## 性能优化建议

### 时间范围优化
- 避免查询过长时间范围（建议不超过7天）
- 使用具体的时间范围而不是开放式查询
- 考虑使用timeunit参数进行时间聚合

### 字段选择优化
- 只查询必要的字段，避免使用 "*"
- 优先使用索引字段作为keys
- 限制返回字段数量（建议不超过10个）

### 过滤条件优化
- 使用具体的过滤条件减少数据量
- 优先使用等值条件（=）而不是范围条件
- 合理使用逻辑运算符（&&, ||）

### 返回记录数优化
- 设置合理的topcount值（建议1000以内）
- 对于大数据量查询，考虑分页处理
- 使用排序字段优化结果

## 常见性能问题
1. 查询时间过长 -> 缩小时间范围或增加过滤条件
2. 返回数据过大 -> 减少字段或限制记录数
3. 查询超时 -> 简化查询条件或分步查询
"""

        agent = MCPMicroAgent(
            name="query_optimization",
            agent_type="knowledge",
            version="1.0.0",
            triggers=["优化", "性能", "慢", "超时", "optimization", "performance"],
            content=content,
            confidence=0.85,
            created_at=datetime.now(),
            last_used=datetime.now(),
            usage_count=0
        )

        self.knowledge_agents[agent.name] = agent
        self._save_microagent(agent, "knowledge")

    def _create_error_handling_agent(self):
        """创建错误处理知识agent"""
        content = """# MCP错误处理指南

## 常见错误码及解决方案

### 认证错误 (错误码: 5)
- 检查API连接是否正确设置
- 确认用户名和密码是否正确
- 重新调用 setup_api_connection

### 表不存在 (错误码: 14, 21)
- 使用 list_statistics_tables 查看可用表
- 检查表名拼写是否正确
- 确认表在当前系统中是否存在

### 字段错误 (错误码: 13, 22)
- 使用 list_table_fields 查看表的可用字段
- 检查字段名拼写是否正确
- 确认字段类型是否匹配查询需求

### 时间格式错误 (错误码: 16)
- 使用标准时间格式: YYYY-MM-DD HH:MM:SS
- 检查开始时间是否早于结束时间
- 确认时间范围是否合理

### 参数错误 (错误码: 26)
- 检查所有必需参数是否提供
- 验证参数值是否在有效范围内
- 确认参数类型是否正确

## 调试建议
1. 从简单查询开始，逐步增加复杂度
2. 使用 get_time_examples 了解时间参数格式
3. 查看 get_api_error_codes 了解所有错误码
4. 检查网络连接和服务器状态
"""

        agent = MCPMicroAgent(
            name="error_handling",
            agent_type="knowledge",
            version="1.0.0",
            triggers=["错误", "失败", "error", "问题", "调试", "debug"],
            content=content,
            confidence=0.9,
            created_at=datetime.now(),
            last_used=datetime.now(),
            usage_count=0
        )

        self.knowledge_agents[agent.name] = agent
        self._save_microagent(agent, "knowledge")

    def _save_microagent(self, agent: MCPMicroAgent, agent_type: str):
        """保存microagent到文件"""
        filename = f"{agent.name}.md"
        filepath = os.path.join(self.microagents_dir, agent_type, filename)

        # 创建YAML前置内容
        yaml_content = f"""---
name: {agent.name}
type: {agent.agent_type}
version: {agent.version}
triggers: {json.dumps(agent.triggers)}
confidence: {agent.confidence}
---

{agent.content}"""

        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(yaml_content)

    def find_relevant_knowledge(self, query: str) -> List[Dict[str, Any]]:
        """根据查询找到相关知识"""
        relevant_knowledge = []

        # 搜索知识型microagents
        for name, agent in self.knowledge_agents.items():
            trigger = agent.match_trigger(query)
            if trigger:
                # 更新使用统计
                agent.usage_count += 1
                agent.last_used = datetime.now()

                relevant_knowledge.append({
                    "name": agent.name,
                    "type": agent.agent_type,
                    "trigger": trigger,
                    "content": agent.content,
                    "confidence": agent.confidence,
                    "usage_count": agent.usage_count
                })

        # 按置信度和使用次数排序
        relevant_knowledge.sort(key=lambda x: (x["confidence"], x["usage_count"]), reverse=True)

        return relevant_knowledge

    def find_task_guidance(self, task_description: str) -> Optional[Dict[str, Any]]:
        """根据任务描述找到相关任务指导"""
        # 搜索任务型microagents
        for name, agent in self.task_agents.items():
            trigger = agent.match_trigger(task_description)
            if trigger:
                # 更新使用统计
                agent.usage_count += 1
                agent.last_used = datetime.now()

                return {
                    "name": agent.name,
                    "type": agent.agent_type,
                    "trigger": trigger,
                    "content": agent.content,
                    "confidence": agent.confidence,
                    "usage_count": agent.usage_count
                }

        return None

    def add_knowledge_agent(self, name: str, content: str, triggers: List[str],
                          confidence: float = 0.8) -> MCPMicroAgent:
        """添加新的知识型microagent"""
        agent = MCPMicroAgent(
            name=name,
            agent_type="knowledge",
            version="1.0.0",
            triggers=triggers,
            content=content,
            confidence=confidence,
            created_at=datetime.now(),
            last_used=datetime.now(),
            usage_count=0
        )

        self.knowledge_agents[agent.name] = agent
        self._save_microagent(agent, "knowledge")
        return agent

    def add_task_agent(self, name: str, content: str, triggers: List[str],
                     confidence: float = 0.8) -> MCPMicroAgent:
        """添加新的任务型microagent"""
        agent = MCPMicroAgent(
            name=name,
            agent_type="task",
            version="1.0.0",
            triggers=triggers,
            content=content,
            confidence=confidence,
            created_at=datetime.now(),
            last_used=datetime.now(),
            usage_count=0
        )

        self.task_agents[agent.name] = agent
        self._save_microagent(agent, "task")
        return agent

    def update_agent(self, name: str, content: str = None,
                   triggers: List[str] = None, confidence: float = None) -> bool:
        """更新现有的microagent"""
        # 检查知识型microagents
        if name in self.knowledge_agents:
            agent = self.knowledge_agents[name]
            if content is not None:
                agent.content = content
            if triggers is not None:
                agent.triggers = triggers
            if confidence is not None:
                agent.confidence = confidence

            self._save_microagent(agent, "knowledge")
            return True

        # 检查任务型microagents
        if name in self.task_agents:
            agent = self.task_agents[name]
            if content is not None:
                agent.content = content
            if triggers is not None:
                agent.triggers = triggers
            if confidence is not None:
                agent.confidence = confidence

            self._save_microagent(agent, "task")
            return True

        return False

    def remove_agent(self, name: str) -> bool:
        """删除microagent"""
        # 检查知识型microagents
        if name in self.knowledge_agents:
            agent = self.knowledge_agents.pop(name)
            filepath = os.path.join(self.microagents_dir, "knowledge", f"{name}.md")
            if os.path.exists(filepath):
                os.remove(filepath)
            return True

        # 检查任务型microagents
        if name in self.task_agents:
            agent = self.task_agents.pop(name)
            filepath = os.path.join(self.microagents_dir, "tasks", f"{name}.md")
            if os.path.exists(filepath):
                os.remove(filepath)
            return True

        return False

    def get_all_agents(self) -> Dict[str, List[Dict[str, Any]]]:
        """获取所有microagents的信息"""
        knowledge_agents_info = []
        for name, agent in self.knowledge_agents.items():
            knowledge_agents_info.append({
                "name": agent.name,
                "type": agent.agent_type,
                "version": agent.version,
                "triggers": agent.triggers,
                "confidence": agent.confidence,
                "created_at": agent.created_at,
                "last_used": agent.last_used,
                "usage_count": agent.usage_count,
                "content_preview": agent.content[:100] + "..." if len(agent.content) > 100 else agent.content
            })

        task_agents_info = []
        for name, agent in self.task_agents.items():
            task_agents_info.append({
                "name": agent.name,
                "type": agent.agent_type,
                "version": agent.version,
                "triggers": agent.triggers,
                "confidence": agent.confidence,
                "created_at": agent.created_at,
                "last_used": agent.last_used,
                "usage_count": agent.usage_count,
                "content_preview": agent.content[:100] + "..." if len(agent.content) > 100 else agent.content
            })

        return {
            "knowledge_agents": knowledge_agents_info,
            "task_agents": task_agents_info
        }